#!/usr/bin/env python3
"""
Setup script for PMS (Payroll Management System)
This script helps initialize the project environment.
"""

import os
import sys
import subprocess
import secrets

def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"✗ Error during {description}: {e}")
        print(f"Error output: {e.stderr}")
        return None

def generate_secret_key():
    """Generate a Django secret key."""
    return secrets.token_urlsafe(50)

def setup_environment():
    """Set up the development environment."""
    print("Setting up PMS Development Environment")
    print("=" * 50)
    
    # Check if virtual environment exists
    if not os.path.exists('venv'):
        print("Creating virtual environment...")
        run_command('python3 -m venv venv', 'Virtual environment creation')
    else:
        print("✓ Virtual environment already exists")
    
    # Activate virtual environment and install dependencies
    if os.path.exists('requirements.txt'):
        print("Installing dependencies...")
        activate_cmd = 'source venv/bin/activate' if os.name != 'nt' else 'venv\\Scripts\\activate'
        install_cmd = f'{activate_cmd} && pip install -r requirements.txt'
        run_command(install_cmd, 'Dependencies installation')
    
    # Update .env file with generated secret key if needed
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            content = f.read()
        
        if 'your-secret-key-here-change-this-in-production' in content:
            new_secret_key = generate_secret_key()
            content = content.replace('your-secret-key-here-change-this-in-production', new_secret_key)
            
            with open('.env', 'w') as f:
                f.write(content)
            print("✓ Generated new SECRET_KEY in .env file")
    
    print("\n" + "=" * 50)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Update the .env file with your database and email settings")
    print("2. Activate the virtual environment: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)")
    print("3. Run migrations: python manage.py migrate")
    print("4. Create a superuser: python manage.py createsuperuser")
    print("5. Start the development server: python manage.py runserver")

if __name__ == '__main__':
    setup_environment()
