{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card card-primary card-outline"> 
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>

            <div class="card-tools">
              <a href="{% url 'manage_designation' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add Designation
              </a>
            </div>
          </div>
          
          <div class="card-body">

            <table id="example1" class="table table-bordered table-striped">
              <thead class="thead-dark">
                <tr>
                  <th>#</th>
                  <th>Department</th>
                  <th>Designation Name</th>
                  <th class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for designation in designations %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ designation.department.name }}</td>  
                  <td>{{ designation.name }}</td>
                  <td class="text-center">
                    <a href="{% url 'manage_designation' designation.id %}" class="btn btn-info btn-sm">
                      <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'delete_designation' designation.id %}" class="btn btn-danger btn-sm"
                        onclick="return confirm('Are you sure you want to delete this designation?')">
                      <i class="fas fa-trash"></i> Delete
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div> 
        </div> 
      </div> 
    </div>
  </div>
</section>

{% endblock content %}