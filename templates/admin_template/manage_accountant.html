{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card card-primary card-outline">
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>

            <div class="card-tools">
              <a href="{% url 'add_accountant' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add Accountant
              </a>
            </div>
          </div>
          <div class="card-body">
            <table id="example1" class="table table-bordered table-striped">
              <thead class="thead-dark">
                <tr>
                  <th>#</th>
                  <th>Full Name</th>
                  <th>Email</th>
                  <th>Gender</th>
                  <th>Avatar</th>
                  <th class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for accountant in allAccountant %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ accountant.first_name }} {{ accountant.last_name }}</td>
                  <td>{{ accountant.email }}</td>
                  <td>{{ accountant.gender }}</td>
                  <td>
                    {% if accountant.profile_pic %}
                      <img class="img img-fluid mb-2" height="56" width="56" src="{{ accountant.profile_pic }}" alt="Accountant Avatar">
                    {% else %}
                      No Image
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <a href="{% url 'edit_accountant' accountant.accountant.id %}" class="btn btn-info btn-sm">
                      <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'delete_accountant' accountant.accountant.id %}" class="btn btn-danger btn-sm"
                        onclick="return confirm('Are you sure you want to delete this accountant?')">
                      <i class="fas fa-trash"></i> Delete
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div> 
        </div> 
      </div> 
    </div>
  </div>
</section>

{% endblock content %}
