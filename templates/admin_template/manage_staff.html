{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">{{ page_title }}</h3>

                        <div class="card-tools">
                            <a href="{% url 'add_staff' %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Add Staff
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table id="example1" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>#</th>
                                    <th>Full Name</th>
                                    <th>Email</th>
                                    <th>Gender</th>
                                    <th>Department</th>
                                    <th>Designation</th>
                                    <th>Employee Type</th>
                                    <th>Avatar</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for staff in staffs %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ staff.first_name }} {{ staff.last_name }}</td>
                                    <td>{{ staff.email }}</td>
                                    <td>{{ staff.gender }}</td>
                                    <td>{{ staff.staff.department }}</td>
                                    <td>{{ staff.staff.designation }}</td>
                                    <td>{{ staff.staff.employment_type}}</td>
                                    <td>
                                        {% if staff.profile_pic %}
                                        <img class="img-circle elevation-2" style="width: 56px; height: 56px;"
                                            src="{{ staff.profile_pic }}" alt="Staff Avatar">
                                        {% else %}
                                        No Image
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <a href="{% url 'edit_staff' staff.staff.id %}" class="btn btn-info btn-sm">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        {% if staff.is_active == 1%}
                                        <a href="{% url 'delete_staff' staff.staff.id %}" class="btn btn-danger btn-sm"
                                            onclick="return confirm('Are you sure you want to delete this staff member?')">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}