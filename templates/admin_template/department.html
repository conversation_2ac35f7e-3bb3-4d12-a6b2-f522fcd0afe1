{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12"> 
        <div class="card card-primary card-outline"> 
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>

            <div class="card-tools">
              <a href="{% url 'manage_department' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add Department
              </a>
            </div>
          </div>
          
          <div class="card-body">
            <table id="example1" class="table table-bordered table-striped">
              <thead class="thead-dark">
                <tr>
                  <th>#</th>
                  <th>Department Code</th>
                  <th>Department Name</th>
                  <th>Division</th>
                  <th class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for department in departments %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ department.code }}</td>
                  <td>{{ department.name }}</td>
                  <td>{{ department.division }}</td>  
                  <td class="text-center">
                    <a href="{% url 'manage_department' department.id %}" class="btn btn-info btn-sm">
                      <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'delete_department' department.id %}" class="btn btn-danger btn-sm"
                        onclick="return confirm('Are you sure you want to delete this department?')">
                      <i class="fas fa-trash"></i> Delete
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div> 
        </div> 
      </div> 
    </div>
  </div>
</section>

{% endblock content %}

