{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Manage Fixed Allowances{% endblock page_title %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card card-primary card-outline">
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>
            <div class="card-tools">
              <a href="{% url 'manage_fixed' %}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Add Fixed %
              </a>
            </div>
          </div>

          <div class="card-body">

            <table id="example1" class="table table-bordered table-striped">
              <thead class="thead-dark">
                <tr class="text-center">
                  <th>Division</th>
                  <th>Month</th>
                  <th>DA (%)</th>
                  <th>HRA (%)</th>
                  <th >Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for allowance in fixed_allowances %}
                  <tr class="text-center">
                    <td>{{ allowance.division }}</td>
                    <td>{{ allowance.month|date:"F Y" }}</td>  
                    <td>{{ allowance.da }}</td>
                    <td>{{ allowance.hra }}</td>
                    <td>
                      <a href="{% url 'manage_fixed' allowance.id %}" class="btn btn-info btn-sm">
                        <i class="fas fa-edit"></i> Edit
                      </a>
                      <a href="{% url 'delete_fixed' allowance.id %}" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this fixed allowance?')">
                        <i class="fas fa-trash"></i> Delete
                      </a>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock content %}
