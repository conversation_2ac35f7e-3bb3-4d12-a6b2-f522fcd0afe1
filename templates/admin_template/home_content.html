{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Admin Dashboard{% endblock page_title %}

{% block content %}
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3>{{ total_accountant }}</h3>
                        <p>Total Accountants</p>
                    </div>
                    <div class="icon">
                        <i class="ion ion-person-stalker"></i>  
                    </div>
                    <a href="{% url 'manage_accountant' %}" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>

            <div class="col-lg-3 col-6">
                <div class="small-box bg-primary">
                    <div class="inner">
                        <h3>{{ total_staff }}</h3>
                        <p>Total Staff</p>
                    </div>
                    <div class="icon">
                        <i class="ion ion-ios-people"></i> 
                    </div>
                    <a href="{% url 'manage_staff'%}" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>

            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3>{{ active_staff }}</h3>
                        <p>Active Staff</p>
                    </div>
                    <div class="icon">
                        <i class="ion ion-checkmark-circled"></i> 
                    </div>
                    <a href="{% url 'manage_staff'%}?is_active=True" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>

            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>{{ inactive_staff }}</h3> 
                        <p>Inactive Staff</p>
                    </div>
                    <div class="icon">
                        <i class="ion ion-close-circled"></i>
                    </div>
                    <a href="{% url 'manage_staff'%}?is_active=False" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">Staff by Department</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="staffByDepartmentChart"></canvas> 
                    </div>
                </div>
            </div> -->
            
            <div class="col-md-6">
                <div class="card card-warning">
                    <div class="card-header">
                        <h3 class="card-title">Recent Add Staff</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            {% for activity in recent_activities %}
                                <li class="list-group-item">{{ activity }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}

