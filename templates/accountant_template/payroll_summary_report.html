{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Monthly Payroll Summary{% endblock page_title %}

{% block content %}

<section class="content">
   <div class="container-fluid">
      <div class="row">
         <div class="col-md-12">
            <div class="card card-info card-outline">
               <div class="card-header">
                  <h3 class="card-title">{{ page_title }} for {{ month }}</h3>
                  <div class="card-tools">
                     <select id="employment-type-filter" class="form-control form-control-sm">
                        <option value="">All Employee Types</option>
                        <option value="Regular" {% if employment_type == 'Regular' %}selected{% endif %}>Regular</option>
                        <option value="Contract" {% if employment_type == 'Contract' %}selected{% endif %}>Contract
                        </option>
                     </select>
                  </div>
               </div>
               <div class="card-body">
                  <table id="example1" class="table table-bordered table-striped mt-3">
                     <thead>
                        <tr>
                           <th>Emp Code</th>
                           <th>Name</th>
                           <th>Basic Salary (₹)</th>
                           <th>Gross Pay (₹)</th>
                           <th>Total Deductions (₹)</th>
                           <th>Net Pay (₹)</th>
                        </tr>
                     </thead>
                     <tbody>
                        {% for payslip in payslips %}
                        <tr>
                           <td>{{ payslip.staff.emp_code }}</td>
                           <td>{{ payslip.staff.user.get_full_name }}</td>
                           <td>{{ payslip.basic }}</td>
                           <td>{{ payslip.gross_pay }}</td>
                           <td>{{ payslip.total_deductions }}</td>
                           <td>{{ payslip.net_pay }}</td>
                        </tr>
                        {% endfor %}
                     </tbody>
                  </table>

                     <table class="table table-bordered">
                         <thead>
                             <tr>
                                 <th>Total Salary (₹)</th>
                                 <th>Total Deductions (₹)</th>
                                 <th>Download</th>
                             </tr>
                         </thead>
                         <tbody>
                             <tr>
                                 <td>{{ total_salary }}</td>
                                 <td>{{ total_deductions }}</td>
                                 <td>
                                     <a href="{% url 'download_payroll_summary' month year %}" class="btn btn-primary btn-sm">
                                         <i class="fas fa-download"></i> Download
                                     </a>
                                 </td>
                             </tr>
                         </tbody>
                     </table>
               </div>
            </div>
         </div>
      </div>
   </div>
</section>

{% endblock content %}

{% block extra_js %}
<script>
   $('#employment-type-filter').change(function () {
      let month = $('#month').val();
      let year = $('#year').val();
      let employmentType = $(this).val();
      let url = "{% url 'payroll_summary_report' %}";

      if (month) {
         url += "?month=" + month;
      }

      if (year) {
         url += "?year=" + year;
      }

      if (employmentType) {
         url += "?employment_type=" + employmentType;
      }

      window.location.href = url;
   });
</script>
{% endblock extra_js %}