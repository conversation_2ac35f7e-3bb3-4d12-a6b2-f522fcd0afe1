{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Staff List{% endblock page_title %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card card-info card-outline"> 
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>
          </div>

          <div class="card-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <table id="example1" class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Emp Code</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Department</th>
                  <th>Designation</th>
                  <th>Employment Type</th>
                  <th>Active</th>
                </tr>
              </thead>
              <tbody>
                {% for staff in staffs %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  <td>{{ staff.emp_code }}</td>
                  <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                  <td>{{ staff.user.email }}</td>
                  <td>{{ staff.department.name }}</td>
                  <td>{{ staff.designation.name }}</td>
                  <td>{{ staff.employment_type }}</td>
                  <td>
                    {% if staff.user.is_active %}
                      <span class="badge bg-success">Yes</span>
                    {% else %}
                      <span class="badge bg-danger">No</span>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock content %}

