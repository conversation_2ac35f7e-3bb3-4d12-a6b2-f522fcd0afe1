{% load static %}

<div class="row">
    <div class="col-md-3"></div>
    <div class="col-md-6">
        <div class="card card-primary"> 
            <div class="card-header">
                <p class="card-title">Enter details carefully</p>
            </div>            
            <form role="form" method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="card-body">
                    {% for field in form %}
                        <div class="form-group">
                            {% if field.name == 'is_active' %}
                                <div class="form-check">
                                    {{ field }}
                                    <label class="form-check-label" for="{{ field.id_for_label }}">
                                        {{ field.label }}
                                    </label>
                                </div>
                            {% else %}
                                {{ field.label_tag }}
                                {{ field }}
                            {% endif %}

                            {% if field.help_text %}
                                <small class="form-text text-muted">{{ field.help_text|safe }}</small>
                            {% endif %}

                            {% if field.errors %}
                                <div class="invalid-feedback">
                                    {{ field.errors }}
                                </div>
                                <script>
                                    $('#{{ field.id_for_label }}').addClass('is-invalid');
                                </script>
                            {% endif %}
                            {% if field.name == 'email' %}
                                        <span class="error email_error" style="display:none;"></span>
                             {% endif %}
                        </div>
                    {% endfor %}
                </div>

                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="javascript:history.back()" class="btn btn-secondary">Cancel</a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button type="submit" class="btn btn-success">{{ button_text }}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div> 
    </div>
    <div class="col-md-3"></div>
</div>

