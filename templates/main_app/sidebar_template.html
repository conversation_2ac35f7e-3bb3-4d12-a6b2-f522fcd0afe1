{% load static %}
<aside class="main-sidebar sidebar-dark-info elevation-4">
    <a href="#" class="brand-link">
        <img src="{% static 'dist/img/pms.avif' %}" alt="PMS" class="brand-image img-circle elevation-3">
        <span class="brand-text font-weight-light">
            {% if request.user.user_type == 1 %}
            Admin Panel
            {% elif request.user.user_type == 2 %}
            Accountant Panel
            {% else %}
            Staff Panel
            {% endif %}
        </span>
    </a>

    <div class="sidebar">
        <div class="user-panel mt-3 pb-3 mb-3 d-flex">
            <div class="image">
                {% if request.user.profile_pic %}
                <img src="{{ request.user.profile_pic }}" class="img-circle elevation-2" alt="User Image">
                {% else %}
                <img src="{% static 'dist/img/default-avatar.jpeg' %}" class="img-circle elevation-2" alt="User Image">
                {% endif %}
            </div>
            <div class="info">
                <a href="#" class="d-block">{{ user.first_name }}</a>
            </div>
        </div>

        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                {% if request.user.user_type == 1 %}
                <li class="nav-item">
                    <a href="/admin/" class="nav-link {% if '/admin/' == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>Admin Dashboard</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'admin_view_profile' as admin_view_profile %}
                    <a href="{{admin_view_profile}}"
                        class="nav-link {% if admin_view_profile == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-address-card"></i>
                        <p>Update Profile</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'division' as manage_division %}
                    <a href="{{manage_division}}"
                        class="nav-link  {% if manage_division == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-handshake"></i>
                        <p>Division</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'department' as manage_department %}
                    <a href="{{manage_department}}"
                        class="nav-link  {% if manage_department == request.path %} active {% endif %}">
                        <i class="nav-icon far fa-building"></i>
                        <p>Department</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'designation' as manage_designation %}
                    <a href="{{manage_designation}}"
                        class="nav-link  {% if edit_designation == request.path or manage_designation == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-bookmark"></i>
                        <p>Designation</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'grade' as manage_grade %}
                    <a href="{{manage_grade}}"
                        class="nav-link  {% if manage_grade == request.path %} active {% endif %}">
                        <i class="fas fa-book nav-icon"></i>
                        <p>Grade</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'fixed' as manage_fixed %}
                    <a href="{{manage_fixed}}"
                        class="nav-link  {% if manage_fixed == request.path %} active {% endif %}">
                        <i class="fas fa-money-bill-wave"></i>
                        <p>Fixed %</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'manage_accountant' as manage_accountant %}
                    <a href="{{manage_accountant}}"
                        class="nav-link  {%if manage_accountant == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-user-tie""></i>
                            <p>Accountant</p>
                        </a>
                    </li>

                    <li class=" nav-item">
                            {% url 'manage_staff' as manage_staff %}
                            <a href="{{manage_staff}}"
                                class="nav-link  {% if manage_staff == request.path %} active {% endif %}">
                                <i class="nav-icon fas fa-users"></i>
                                <p>Staff</p>
                            </a>
                </li>

                <li class=" nav-item">
                    {% url 'list_payslip' as list_payslip %}
                    <a href="{{list_payslip}}"
                        class="nav-link  {% if list_payslip == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-import"></i>
                        <p>List of Generate Payslip</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'list_payslip_type' 'Regular' as list_payslip_regular %}
                    {% url 'list_payslip_type' 'Contract' as list_payslip_contract %}
                    {% url 'list_payslip_type' 'Active' as list_payslip_active %}

                    <a href="#"
                        class="nav-link left {% if list_payslip_regular == request.path or list_payslip_contract == request.path or list_payslip_active == request.path %} active {% endif %}">

                        <p>Edit Payslip<i class="right fas fa-angle-left"></i></p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class=" nav-item">
                            <a href="{{list_payslip_regular}}"
                                class="nav-link  {% if list_payslip_regular == request.path %} active {% endif %}">
                                <i class="nav-icon fas fa-file-contract"></i>
                                <p>For Regular</p>
                            </a>
                        </li>
                        <li class=" nav-item">
                            <a href="{{list_payslip_contract}}"
                                class="nav-link  {% if list_payslip_contract == request.path %} active {% endif %}">
                                <i class="nav-icon fas fa-file-signature"></i>
                                <p>For Contrect</p>
                            </a>
                        </li>
                        <li class=" nav-item">
                            <a href="{{list_payslip_active}}"
                                class="nav-link  {% if list_payslip_active == request.path %} active {% endif %}">
                                <i class="nav-icon fas fa-file-signature"></i>
                                <p>For Active</p>
                            </a>
                        </li>
                    </ul>
                </li>



                {% elif request.user.user_type == 2 %}
                <li class="nav-item">
                    {% url 'accountant_home' as accountant_home %}
                    <a href="{{accountant_home}}"
                        class="nav-link {% if accountant_home == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-home"></i>
                        <p>Home</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'accountant_view_profile' as accountant_view_profile %}
                    <a href="{{accountant_view_profile}}"
                        class="nav-link {% if accountant_view_profile == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-address-card"></i>
                        <p>View Profile</p>
                    </a>
                </li>
                <li class="nav-header">
                    {% url 'generate_payslip_regular' as generate_payslip_regular %}
                    {% url 'generate_payslip_contract' as generate_payslip_contract %}
                    {% url 'generate_payslip_active' as generate_payslip_active %}

                    <a href="#"
                        class="nav-link left {% if generate_payslip_regular == request.path or generate_payslip_contract == request.path or generate_payslip_active == request.path %} active {% endif %}">

                        <p>Generate Payslip</p>
                    </a>
                </li>

                <li class=" nav-item">
                    <a href="{{generate_payslip_regular}}"
                        class="nav-link  {% if generate_payslip_regular == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-contract"></i>
                        <p>For Regular</p>
                    </a>
                </li>
                <li class=" nav-item">
                    <a href="{{generate_payslip_contract}}"
                        class="nav-link  {% if generate_payslip_contract == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-signature"></i>
                        <p>For Contrect</p>
                    </a>
                </li>
                <li class=" nav-item">
                    <a href="{{generate_payslip_active}}"
                        class="nav-link  {% if generate_payslip_active == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-signature"></i>
                        <p>For Active</p>
                    </a>
                </li>

                <li class="nav-header">
                </li>
                <li class=" nav-item">
                    {% url 'list_payslip' as list_payslip %}
                    <a href="{{list_payslip}}"
                        class="nav-link  {% if list_payslip == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-file-import"></i>
                        <p>List of Generate Payslip</p>
                    </a>
                </li>

                <li class=" nav-item">
                    {% url 'staff_list' as staff_list %}
                    <a href="{{staff_list}}" class="nav-link  {% if staff_list == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-users"></i>
                        <p>List of Staff</p>
                    </a>
                </li>

                <li class=" nav-item">
                    {% url 'payroll_summary_report' as payroll_summary_report %}
                    <a href="{{payroll_summary_report}}" class="nav-link  {% if payroll_summary_report == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-clipboard-list"></i>
                        <p>Summary Report</p>
                    </a>
                </li>


                {% elif user.user_type == 3 %}
                <li class="nav-item">
                    {% url 'staff_home' as staff_home %}
                    <a href="{{staff_home}}" class="nav-link {% if staff_home == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-home"></i>
                        <p>
                            Home

                        </p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'staff_view_profile' as staff_view_profile %}
                    <a href="{{staff_view_profile}}"
                        class="nav-link {% if staff_view_profile == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-address-card"></i>
                        <p>View Profile</p>
                    </a>
                </li>

                <li class="nav-item">
                    {% url 'staff_list_payslips' as staff_list_payslips %}
                    <a href="{{staff_list_payslips}}"
                        class="nav-link {% if staff_list_payslips == request.path %} active {% endif %}">
                        <i class="nav-icon fas fa-list-ul"></i>
                        <p>List of Payslip</p>
                    </a>
                </li>

                {% endif %}


                {% if user.is_authenticated %}
                <li class="nav-item">
                    <a href="{% url 'user_logout' %}"
                        onclick="return confirm('Your session would be terminated.\n\nProceed?')" class="nav-link">
                        <i class="nav-icon fas fa-power-off"></i>
                        <p>
                            Logout
                        </p>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</aside>