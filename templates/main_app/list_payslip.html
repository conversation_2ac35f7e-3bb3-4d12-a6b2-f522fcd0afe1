{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block content %}
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">{{ page_title }}</h3>
                    </div>

                    <form id="send-email-form" method="post" action="{% url 'send_bulk_email' %}">
                        {% csrf_token %}

                        <div class="card-body">
                            <table id="example1" class="table table-bordered table-striped">
                                <thead class="thead-dark">
                                    <tr>
                                        <th><input type="checkbox" id="select_all_checkbox"></th>
                                        <th>Emp Code</th>
                                        <th>Name</th>
                                        <th>Department</th>
                                        <th>Designation</th>
                                        <th>Month</th>
                                        <th>Basic Salary (₹)</th>
                                        <th>Gross Pay (₹)</th>
                                        <th>Total Deductions (₹)</th>
                                        <th>Net Pay (₹)</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for staff in staffs %}
                                    {% for payslip in staff.payslip_history %}
                                    <tr>
                                        <td><input type="checkbox" class="staff-checkbox" name="staffs"
                                                value="{{ staff.id }}-{{ payslip.month|date:'Y-m-d' }}"></td>
                                        <td>{{ staff.emp_code }}</td>
                                        <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                        <td>{{ staff.department.name }}</td>
                                        <td>{{ staff.designation.name }}</td>
                                        <td>{{ payslip.month|date:"F Y" }}</td>
                                        <td class="text-right">{{ payslip.basic }}</td>
                                        <td class="text-right">{{ payslip.gross_pay }}</td>
                                        <td class="text-right">{{ payslip.total_deductions }}</td>
                                        <td class="text-right">{{ payslip.net_pay }}</td>
                                        <td class="text-center">
                                            <a href="{% url 'view_payslip' staff.id payslip.month|date:'Y-m-d' %}"
                                                class="btn btn-success btn-sm">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <input type="hidden" name="month" class="payslip-month"
                                                value="{{ payslip.month|date:'Y-m-d' }}">
                                            <input type="hidden" name="staff" class="staff-id" value="{{ staff.id }}">
                                            <button type="button" class="btn btn-primary send-email-btn">
                                                <i class="fas fa-envelope"></i> Send
                                            </button>
                                        </td>
                                    </tr>

                                    {% endfor %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="card-footer">
                            {% if user.user_type == 1 %}
                            <button type="button" class="btn btn-primary" id="edit-payslip">
                                <i class="fas fa-edit"></i> Edit Selected Payslips
                            </button>
                            {% endif %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Send to Selected Payslips
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock content %}

{% block extra_js %}
<script>
    $(document).ready(function () {
        // Function to get CSRF token from cookies
        function getCookie(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = jQuery.trim(cookies[i]);
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Handle individual Payslip Email Sending (`.send-email-btn` Click):
        $(document).on('click', '.send-email-btn', function () {
            var staffId = $(this).siblings('.staff-id').val();
            var month = $(this).siblings('.payslip-month').val();
            var csrftoken = getCookie('csrftoken'); // Get CSRF token
            var button = $(this);
            $.ajax({
                url: '/send_email/' + staffId + '/' + month + '/',
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken }, // Include CSRF token in headers
                beforeSend: function () {
                    button.prop('disabled', true); // Disable the button
                    button.html('<i class="fas fa-spinner fa-spin"></i> Sending...');
                },
                success: function (response) {
                    if (response.success) {
                        toastr.success('Email sent successfully!'); // Use Toastr for success
                    } else {
                        toastr.error(response.error); // Use Toastr for error
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error('An error occurred while sending the email.'); // Use Toastr for error
                },
                complete: function () {
                    button.prop('disabled', false); // Enable the button after the request is complete
                    button.html('<i class="fas fa-envelope"></i> Send');
                }
            });
        });

        // Handle Bulk Email Sending (`#send-email-form` Submit):
        $('#send-email-form').submit(function (event) {
            event.preventDefault();

            $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Sending...');

            var selectedStaff = $('.staff-checkbox:checked').map(function () {
                return {
                    staffId: $(this).val(),
                    month: $(this).closest('tr').find('.payslip-month').val()
                };
            }).get();

            if (selectedStaff.length === 0) {
                toastr.error('No staff selected!'); // Use Toastr for error
                $('#send-email-form button[type="submit"]').prop('disabled', false).text('Send Emails to Selected');
                return;
            }

            var csrftoken = getCookie('csrftoken');
            $.ajax({
                url: '/send_bulk_email/',
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken },
                data: JSON.stringify({ 'staffs': selectedStaff }),
                contentType: "application/json",
                success: function (response) {
                    if (response.success) {
                        toastr.success(response.message); // Use Toastr for success
                    } else {
                        if (typeof response.errors === 'object') {
                            for (const field in response.errors) {
                                toastr.error(response.errors[field]); // Use Toastr for error
                            }
                        } else {
                            toastr.error(response.errors);  // Use Toastr for error
                        }
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error("An unexpected error occurred.");  // Use Toastr for error
                },
                complete: function () {
                    $('#send-email-form button[type="submit"]').prop('disabled', false).text('Send Emails to Selected');
                }
            });

        });

        $('#edit-payslip').click(function () {

            var selectedStaff = $('.staff-checkbox:checked').map(function () {
                return {
                    staffId: $(this).val(),
                    month: $(this).closest('tr').find('.payslip-month').val()
                };
            }).get();

            if (selectedStaff.length === 0) {
                toastr.error('No staff selected!'); // Use Toastr for error
                return;
            }

            var csrftoken = getCookie('csrftoken');
            $.ajax({
                url: '/admin/edit/payslip/', // Correct URL for edit payslip
                type: 'POST',
                headers: { 'X-CSRFToken': csrftoken },
                data: JSON.stringify({ 'staffs': selectedStaff }),
                contentType: "application/json",
                success: function (response) {
                    if (response) {
                        var newWindow = window.open();
                        if (newWindow) {
                            newWindow.document.open();
                            newWindow.document.write(response);
                            newWindow.document.close();
                        } else {
                            toastr.error('Failed to open a new tab. Please check your pop-up settings.');
                        }
                    } else {
                        toastr.error('No response received from the server.');
                    }
                },
                error: function (xhr, status, error) {
                    toastr.error("An unexpected error occurred.");  // Use Toastr for error
                },
            });
        });

        $('#select_all_checkbox').change(function () {
            $('.staff-checkbox').prop('checked', this.checked);
        });
    });
</script>
{% endblock extra_js %}