{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Payroll Management System{% endblock title %}</title>

    <link rel="stylesheet" href="{% static 'plugins/fontawesome-free/css/all.min.css' %}">
    <link rel="stylesheet" href="{% static 'dist/css/adminlte.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/toastr/toastr.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/datatables-bs4/css/dataTables.bootstrap4.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/datatables-responsive/css/responsive.bootstrap4.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/datatables-buttons/css/buttons.bootstrap4.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/select2/css/select2.min.css' %}">
    <link rel="stylesheet" href="{% static 'plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css' %}">
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">


    {% block extra_css %}{% endblock extra_css %}
    <script src="{% static 'plugins/jquery/jquery.min.js' %}"></script>
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700" rel="stylesheet">
</head>

<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#"><i class="fas fa-bars"></i></a>
                </li>
            </ul>
        </nav>

        {% include "main_app/sidebar_template.html" with user=user %}

        <div class="content-wrapper">
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0 text-dark">{% block page_title %}{% endblock page_title %}</h1>
                        </div><!-- /.col -->
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item"><a href="#">Home</a></li>
                                <li class="breadcrumb-item active">{{ page_title }}</li>
                            </ol>
                        </div><!-- /.col -->
                    </div>
                </div>
            </section>

            <section class="content">
                <div class="container-fluid">
                    {% block content %}
                    {% endblock content %}
                </div>
            </section>
        </div>
        {% include "main_app/footer.html" %}
    </div>

    <script src="{% static 'plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'dist/js/adminlte.min.js' %}"></script>
    <script src="{% static 'plugins/toastr/toastr.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/jquery.dataTables.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-bs4/js/dataTables.bootstrap4.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-responsive/js/dataTables.responsive.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-responsive/js/responsive.bootstrap4.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/dataTables.buttons.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.bootstrap4.min.js' %}"></script>
    <script src="{% static 'plugins/jszip/jszip.min.js' %}"></script>
    <script src="{% static 'plugins/pdfmake/pdfmake.min.js' %}"></script>
    <script src="{% static 'plugins/pdfmake/vfs_fonts.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.html5.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.print.min.js' %}"></script>
    <script src="{% static 'plugins/datatables-buttons/js/buttons.colVis.min.js' %}"></script>
    <script src="{% static 'plugins/select2/js/select2.full.min.js' %}"></script>

    <script>
        $(document).ready(function () {
            toastr.options = {
                closeButton: true,
                positionClass: "toast-top-right",
            };
            {% if messages %}
            {% for message in messages %}
            toastr["{{ message.tags }}"]("{{ message|escapejs }}");
            {% endfor %}
            {% endif %}
        });
    </script>

    <script>
        $(document).ready(function () {
            $('.select2bs4').select2({
                theme: 'bootstrap4'
            });
        });

        $(function () {
            $("#example1, #example2, #example3").DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "buttons": ["excel", "pdf", "print", "colvis"],
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]]
            }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0), #example2_wrapper .col-md-6:eq(0), #example3_wrapper .col-md-6:eq(0)');
        });
    </script>

    {% block extra_js %}{% endblock extra_js %}

</body>

</html>