# Generated by Django 4.2.21 on 2025-05-28 09:12

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import main.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.Char<PERSON><PERSON>(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True, validators=[django.core.validators.EmailValidator()])),
                ('user_type', models.PositiveSmallIntegerField(choices=[(1, 'Admin'), (2, 'Accountant'), (3, 'Staff')], default=1)),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], default='M', max_length=1)),
                ('profile_pic', models.ImageField(blank=True, null=True, upload_to='')),
                ('father_name', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', main.models.CustomUserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Accountant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.CreateModel(
            name='Admin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('paid_days', models.FloatField(null=True)),
                ('lop', models.FloatField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='ContractPay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.DateField(null=True)),
                ('adhoc', models.PositiveIntegerField(null=True)),
                ('hra', models.PositiveIntegerField(null=True)),
                ('arrears', models.PositiveIntegerField(null=True)),
                ('other', models.PositiveIntegerField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Deductions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('income_tax', models.PositiveIntegerField(null=True)),
                ('canteen', models.PositiveIntegerField(null=True)),
                ('advance', models.PositiveIntegerField(null=True)),
                ('society', models.PositiveIntegerField(null=True)),
                ('insurance', models.PositiveIntegerField(null=True)),
                ('other', models.PositiveIntegerField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(db_index=True, max_length=10, unique=True)),
                ('name', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Designation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Division',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Fixed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.DateField(null=True)),
                ('da', models.FloatField(null=True)),
                ('hra', models.FloatField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Grade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start', models.PositiveIntegerField()),
                ('end', models.PositiveIntegerField()),
                ('increment', models.PositiveIntegerField()),
                ('medical', models.PositiveIntegerField(default=0)),
                ('adhoc', models.PositiveIntegerField()),
                ('conva', models.PositiveIntegerField()),
                ('cca', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Staff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('emp_code', models.CharField(max_length=20, unique=True)),
                ('uan', models.CharField(max_length=20, null=True)),
                ('emp_doj', models.DateField(null=True)),
                ('basic_amt', models.PositiveIntegerField(null=True)),
                ('employment_type', models.CharField(choices=[('Regular', 'Regular'), ('Contract', 'Contract'), ('Active', 'Active')], default='Regular', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('cca', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='main.department')),
                ('designation', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='main.designation')),
                ('division', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='main.division')),
                ('grade', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='main.grade')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RegularPay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('arrears', models.PositiveIntegerField(null=True)),
                ('other', models.PositiveIntegerField(null=True)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.staff')),
            ],
        ),
        migrations.CreateModel(
            name='Payslip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.DateField()),
                ('basic', models.PositiveIntegerField(default=0)),
                ('da', models.PositiveIntegerField(default=0)),
                ('dp', models.PositiveIntegerField(default=0)),
                ('society', models.PositiveIntegerField(default=0)),
                ('hra', models.PositiveIntegerField(default=0)),
                ('conv', models.PositiveIntegerField(default=0)),
                ('medical', models.PositiveIntegerField(default=0)),
                ('cca', models.PositiveIntegerField(default=0)),
                ('adhoc', models.PositiveIntegerField(default=0)),
                ('Dother', models.PositiveIntegerField(default=0)),
                ('Pother', models.PositiveIntegerField(default=0)),
                ('arrears', models.PositiveIntegerField(default=0)),
                ('gross_pay', models.PositiveIntegerField(default=0)),
                ('epf', models.PositiveIntegerField(default=0)),
                ('esi', models.PositiveIntegerField(default=0)),
                ('income_tax', models.PositiveIntegerField(default=0)),
                ('canteen', models.PositiveIntegerField(default=0)),
                ('advance', models.PositiveIntegerField(default=0)),
                ('insurance', models.PositiveIntegerField(default=0)),
                ('total_deductions', models.PositiveIntegerField(default=0)),
                ('net_pay', models.PositiveIntegerField(default=0)),
                ('paid_days', models.PositiveIntegerField(default=0)),
                ('lop', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.staff')),
            ],
        ),
        migrations.AddConstraint(
            model_name='grade',
            constraint=models.CheckConstraint(check=models.Q(('start__lte', models.F('end'))), name='start_lte_end'),
        ),
        migrations.AddField(
            model_name='fixed',
            name='division',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='main.division'),
        ),
        migrations.AddField(
            model_name='designation',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.department'),
        ),
        migrations.AddField(
            model_name='department',
            name='division',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.division'),
        ),
        migrations.AddField(
            model_name='deductions',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.staff'),
        ),
        migrations.AddField(
            model_name='contractpay',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.staff'),
        ),
        migrations.AddField(
            model_name='attendance',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='main.staff'),
        ),
        migrations.AddField(
            model_name='admin',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountant',
            name='user',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customuser',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions'),
        ),
        migrations.AlterUniqueTogether(
            name='payslip',
            unique_together={('staff', 'month')},
        ),
        migrations.AlterUniqueTogether(
            name='designation',
            unique_together={('department', 'name')},
        ),
    ]
