import json
import math
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from django.db import IntegrityError
from django.core.exceptions import ObjectDoesNotExist
from django.core.files.storage import FileSystemStorage
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Count, Max
from calendar import monthrange
from datetime import datetime

from .forms import *
from .models import *


def admin_home(request):
    total_accountant = Accountant.objects.count()
    total_staff = Staff.objects.count()
    active_staff = Staff.objects.filter(is_active=True).count()
    inactive_staff = Staff.objects.filter(is_active=False).count()

    staff_by_department = (
        Staff.objects.values("department__name")
        .annotate(count=Count("id"))
        .order_by("-count")
    )
    department_labels = [d["department__name"] for d in staff_by_department]
    department_counts = [d["count"] for d in staff_by_department]

    recent_activities = [
        f"New staff member {staff.user.get_full_name()} joined on {staff.emp_doj.strftime('%Y-%m-%d')}"
        for staff in Staff.objects.order_by("-created_at")[:5]
    ]

    context = {
        'page_title': "Admin Dashboard",
        'total_accountant': total_accountant,
        'total_staff': total_staff,
        'active_staff': active_staff,
        'inactive_staff': inactive_staff,
        'department_labels': department_labels,
        'department_counts': department_counts,
        'recent_activities': recent_activities,
    }

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'department_labels': department_labels,
            'department_counts': department_counts,
            'recent_activities': recent_activities,
        })


    return render(request, "admin_template/home_content.html", context)

@login_required
def admin_view_profile(request):
    # Admin users should use Django admin for profile management
    # Redirect to Django admin user change form
    messages.info(request, "Admin profile management is now handled through Django Admin.")
    return redirect(f'/admin/main/customuser/{request.user.id}/change/')


def division(request):
    divisions = Division.objects.all()
    context = {
        'divisions': divisions,
        'page_title': 'Manage Divisions'
    }
    return render(request, "admin_template/division.html", context)

def manage_division(request, division_id=None):
    instance = get_object_or_404(Division, id=division_id) if division_id else None
    form = DivisionForm(request.POST or None, instance=instance)
    if request.method == 'POST':
        if form.is_valid():
            try:
                form.save()
                action = "Updated" if instance else "Added"
                messages.success(request, f"Division {action} Successfully!")
            except Exception as e:
                messages.error(request, f"Error saving data: {str(e)}")
            return redirect('division')
        else:
            messages.error(request, "Invalid Data Provided")
    context = {
        'form': form,
        'page_title': 'Add Division' if not instance else 'Edit Division'
    }
    return render(request, 'admin_template/manage_division.html', context)

def delete_division(request, division_id):
    division = get_object_or_404(Division, id=division_id)
    try:
        division.delete()
        messages.success(request, "Division deleted successfully!")
    except IntegrityError as e:
        messages.error(request, f"Cannot delete division: {e}")

    return redirect(reverse('division'))


def department(request):
    departments = Department.objects.all()
    context = {
        'departments': departments,
        'page_title': 'Manage Departments'
    }
    return render(request, "admin_template/department.html", context)

def manage_department(request, department_id=None):
    instance = get_object_or_404(Department, id=department_id) if department_id else None
    form = DepartmentForm(request.POST or None, instance=instance)
    if request.method == 'POST':
        if form.is_valid():
            try:
                form.save()
                action = "Updated" if instance else "Added"
                messages.success(request, f"Department {action} Successfully!")
            except Exception as e:
                messages.error(request, f"Error saving data: {str(e)}")
            return redirect('department')
        else:
            messages.error(request, "Invalid form data. Please check the errors below.")

    context = {
        'form': form,
        'page_title': 'Add Department' if not instance else 'Edit Department'
    }
    return render(request, 'admin_template/manage_department.html', context)

def delete_department(request, department_id):
    department = get_object_or_404(Department, id=department_id)
    try:
        department.delete()
        messages.success(request, "Department deleted successfully!")
    except IntegrityError as e:
        messages.error(request, f"Cannot delete department: {e}")

    return redirect(reverse('department'))


def designation(request):
    designations = Designation.objects.all()
    return render(request, "admin_template/designation.html", {
        'designations': designations,
        'page_title': 'Manage Designations'
    })

def manage_designation(request, designation_id=None):
    instance = get_object_or_404(Designation, id=designation_id) if designation_id else None
    form = DesignationForm(request.POST or None, instance=instance)
    if request.method == 'POST':
        if form.is_valid():
            try:
                form.save()
                action = "Updated" if instance else "Added"
                messages.success(request, f"Designation {action} Successfully!")
            except Exception as e:
                messages.error(request, f"Error saving data: {str(e)}")
            return redirect('designation')
        else:
            messages.error(request, "Invalid Data Provided")

    return render(request, 'admin_template/manage_designation.html', {
        'form': form,
        'page_title': 'Add Designation' if not instance else 'Edit Designation'
    })

def delete_designation(request, designation_id):
    designation = get_object_or_404(Designation, id=designation_id)
    try:
        designation.delete()
        messages.success(request, "Designation deleted successfully!")
    except IntegrityError:
        messages.error(request, "Cannot delete designation: Staff members are currently assigned to it.")

    return redirect(reverse('designation'))


def grade(request):
    grades = Grade.objects.all()
    context = {
        'grades': grades,
        'page_title': 'Manage Grades'
    }
    return render(request, "admin_template/grade.html", context)

def manage_grade(request, grade_id=None):
    instance = get_object_or_404(Grade, id=grade_id) if grade_id else None
    form = GradeForm(request.POST or None, instance=instance)
    if request.method == 'POST':
        if form.is_valid():
            try:
                form.save()
                action = "updated" if instance else "added"
                messages.success(request, f"Grade {action} successfully!")
            except Exception as e:
                messages.error(request, f"Error saving data: {str(e)}")
            return redirect('grade')
        else:
            messages.error(request, "Invalid form data. Please check the errors below.")

    context = {
        'form': form,
        'page_title': 'Add Grade' if not instance else 'Edit Grade'
    }
    return render(request, 'admin_template/manage_grade.html', context)

def delete_grade(request, grade_id):
    grade = get_object_or_404(Grade, id=grade_id)
    try:
        grade.delete()
        messages.success(request, "Grade deleted successfully!")
    except IntegrityError:
        messages.error(request, "Cannot delete grade: Staff members are currently assigned to it.")

    return redirect('grade')


def fixed(request):
    fixed_allowances = Fixed.objects.all()
    context = {
        'fixed_allowances': fixed_allowances,
        'page_title': 'Manage Fixed %',
    }
    return render(request, 'admin_template/fixed.html', context)

def manage_fixed(request, fixed_id=None):
    instance = get_object_or_404(Fixed, pk=fixed_id) if fixed_id else None
    form = FixedForm(request.POST or None, instance=instance)
    if request.method == 'POST':
        if form.is_valid():
            try:
                form.save()
                action = "Updated" if instance else "Added"
                messages.success(request, f"Fixed % {action} successfully!")
            except Exception as e:
                messages.error(request, f"Error saving data: {str(e)}")
            return redirect('fixed')

    return render(request, 'admin_template/manage_fixed.html', {
        'form': form,
        'page_title': 'Add Fixed %' if not instance else 'Edit Fixed %'
    })

def delete_fixed(request, fixed_id):
    fixed = get_object_or_404(Fixed, id=fixed_id)
    fixed.delete()
    messages.success(request, "Fixed allowance deleted successfully!")
    return redirect('fixed')


def manage_accountant(request):
    allAccountant = CustomUser.objects.filter(user_type=2)
    context = {
        'allAccountant': allAccountant,
        'page_title': 'Manage Accountant'
    }
    return render(request, "admin_template/manage_accountant.html", context)

def add_accountant(request):
    if request.method == 'POST':
        form = AccountantForm(request.POST, request.FILES)
        if form.is_valid():
            first_name = form.cleaned_data.get('first_name')
            last_name = form.cleaned_data.get('last_name')
            father_name = form.cleaned_data.get('father_name')
            email = form.cleaned_data.get('email')
            gender = form.cleaned_data.get('gender')
            password = form.cleaned_data.get('password')
            passport = request.FILES.get('profile_pic')
            if passport != None:
                fs = FileSystemStorage()
                filename = fs.save(passport.name, passport)
                passport_url = fs.url(filename)
            else:
                passport_url = None

            try:
                user = CustomUser.objects.create_user(
                    email=email,
                    password=password,
                    user_type=2,
                    first_name=first_name,
                    last_name=last_name,
                    profile_pic=passport_url,
                    gender = gender,
                    father_name = father_name
                    )
                user.save()
                messages.success(request, "Accountant added successfully!")
                return redirect('manage_accountant')

            except Exception as e:
                messages.error(request, "Could Not Add " + str(e))
        else:
            messages.error(request, "Invalid Data Provided")
    else:
        form = AccountantForm()

    context = {'form': form, 'page_title': 'Add Accountant'}
    return render(request, 'admin_template/add_accountant_template.html', context)

def edit_accountant(request, accountant_id):
    try:
        accountant = get_object_or_404(Accountant, id=accountant_id)
        form = AccountantForm(request.POST or None, instance=accountant)
        context = {
            'form': form,
            'accountant_id': accountant_id,
            'page_title': 'Edit Accountant',
            'accountant': accountant
        }
        if request.method == 'POST':
            if form.is_valid():
                first_name = form.cleaned_data.get('first_name')
                last_name = form.cleaned_data.get('last_name')
                username = form.cleaned_data.get('username')
                email = form.cleaned_data.get('email')
                gender = form.cleaned_data.get('gender')
                father_name = form.cleaned_data.get('father_name')
                password = form.cleaned_data.get('password') or None
                passport = request.FILES.get('profile_pic') or None
                try:
                    user = CustomUser.objects.get(id=accountant.user.id)
                    user.username = username
                    user.email = email
                    if password != None:
                        user.set_password(password)
                    if passport != None:
                        fs = FileSystemStorage()
                        filename = fs.save(passport.name, passport)
                        passport_url = fs.url(filename)
                        user.profile_pic = passport_url
                    user.first_name = first_name
                    user.last_name = last_name
                    user.gender = gender
                    user.father_name= father_name
                    user.save()
                    messages.success(request, "Successfully Updated")
                    return redirect(reverse('manage_accountant'))
                except Exception as e:
                    messages.error(request, "Could Not Update " + str(e))
            else:
                messages.error(request, "Please fil form properly")
        else:
            user = CustomUser.objects.get(accountant__id=accountant_id)
            return render(request, "admin_template/edit_accountant_template.html", context)
    except Exception as e:
        messages.error(request, "Accountant member not found."+ str(e))
        return redirect('manage_accountant')

def delete_accountant(request, accountant_id):
    try:
        accountant = Accountant.objects.get(id=accountant_id)
        user = accountant.user
        user.delete()
        messages.success(request, "Accountant deleted successfully!")
    except ObjectDoesNotExist:
        messages.error(request, "Accountant not found.")
    except IntegrityError as e:
        messages.error(request, f"Cannot delete accountant: {e}")
    return redirect(reverse('manage_accountant'))


def manage_staff(request):
    is_active_filter = request.GET.get('is_active', 'True')
    staffs = CustomUser.objects.filter(user_type=3, staff__is_active=is_active_filter)
    context = {
        'staffs': staffs,
        'page_title': 'Manage Staff'
    }
    return render(request, "admin_template/manage_staff.html", context)

def add_staff(request):
    staff_form = StaffForm(request.POST or None, request.FILES or None)
    context = {'form': staff_form,
                'page_title': 'Add Staff',
                'divisions': Division.objects.all(),
                'departments': Department.objects.all(),
                'designations': Designation.objects.all(),
                'grades': Grade.objects.all(),
                'gender': GENDER_CHOICES,
                'Emp_choices':EMPLOYMENT_TYPE,}
    if request.method == 'POST':
        if staff_form.is_valid():
             first_name = staff_form.cleaned_data.get('first_name')
             last_name = staff_form.cleaned_data.get('last_name')
             email = staff_form.cleaned_data.get('email')
             gender = staff_form.cleaned_data.get('gender')
             father_name = staff_form.cleaned_data.get('father_name')
             division = staff_form.cleaned_data.get('division')
             designation = staff_form.cleaned_data.get('designation')
             department = staff_form.cleaned_data.get('department')
             emp_code = staff_form.cleaned_data.get('emp_code')
             uan = staff_form.cleaned_data.get('uan')
             emp_doj = staff_form.cleaned_data.get('emp_doj')
             grade = staff_form.cleaned_data.get('grade')
             basic_amt = staff_form.cleaned_data.get('basic_amt')
             employment_type = staff_form.cleaned_data.get('employment_type')
             cca = staff_form.cleaned_data.get('cca')
             password = staff_form.cleaned_data.get('password') or None
             passport = request.FILES.get('profile_pic') or None
             if password == None:
                password ='Apoorv@PMS#003'
             if passport != None:
                fs = FileSystemStorage()
                filename = fs.save(passport.name, passport)
                passport_url = fs.url(filename)
             else:
                passport_url = ""

             try:
                user = CustomUser.objects.create_user(
                    email=email,
                    password=password,
                    user_type=3,
                    first_name=first_name,
                    last_name=last_name,
                    profile_pic=passport_url ,
                    gender=gender,
                    father_name=father_name
                )

                user.staff.employment_type=employment_type
                user.staff.designation=designation
                user.staff.division=division
                user.staff.department=department
                user.staff.emp_code=emp_code
                user.staff.uan=uan
                user.staff.emp_doj=emp_doj
                user.staff.grade=grade
                user.staff.basic_amt=basic_amt
                user.staff.cca = cca

                user.save()

                messages.success(request, "Successfully Added")
                return redirect('manage_staff')
             except Exception as e:
                messages.error(request, "Could Not Add: " + str(e))
        else:
             messages.error(request, "Could Not Add: ")
    return render(request, 'admin_template/add_staff_template.html', context)

def edit_staff(request, staff_id):
    try:
        staff = get_object_or_404(Staff, id=staff_id)
        form = StaffForm(request.POST or None, instance=staff)
        context = {
            'form': form,
            'staff_id': staff_id,
            'page_title': 'Edit Staff',
            'staff': staff
        }
        if request.method == 'POST':
            if form.is_valid():
                first_name = form.cleaned_data.get('first_name')
                last_name = form.cleaned_data.get('last_name')
                email = form.cleaned_data.get('email')
                gender = form.cleaned_data.get('gender')
                father_name = form.cleaned_data.get('father_name')
                designation = form.cleaned_data.get('designation')
                department = form.cleaned_data.get('department')
                emp_code = form.cleaned_data.get('emp_code')
                uan = form.cleaned_data.get('uan')
                emp_doj = form.cleaned_data.get('emp_doj')
                grade = form.cleaned_data.get('grade')
                basic_amt = form.cleaned_data.get('basic_amt')
                employment_type = form.cleaned_data.get('employment_type')
                is_active = form.cleaned_data.get('is_active')
                cca = form.cleaned_data.get('cca')
                password = form.cleaned_data.get('password') or None
                passport = request.FILES.get('profile_pic') or None
                try:
                    user = CustomUser.objects.get(id=staff.user.id)
                    if passport != None:
                        fs = FileSystemStorage()
                        filename = fs.save(passport.name, passport)
                        passport_url = fs.url(filename)
                        user.profile_pic = passport_url
                    user.email = email
                    if password != None:
                        user.set_password(password)
                    user.first_name = first_name
                    user.last_name = last_name
                    user.father_name = father_name
                    user.gender = gender
                    user.staff.designation=designation
                    user.staff.department=department
                    user.staff.emp_code=emp_code
                    user.staff.uan=uan
                    user.staff.emp_doj=emp_doj
                    user.staff.grade=grade
                    user.staff.basic_amt=basic_amt
                    user.staff.employment_type=employment_type
                    user.staff.cca=cca
                    user.staff.is_active = is_active
                    user.is_active = is_active
                    user.save()
                    messages.success(request, "Successfully Updated")
                    return redirect(reverse('manage_staff'))
                except Exception as e:
                    messages.error(request, "Could Not Update " + str(e))
            else:
                messages.error(request, "Please Fill Form Properly!")
        else:
            return render(request, "admin_template/edit_staff_template.html", context)
    except ObjectDoesNotExist:
        messages.error(request, "Staff member not found.")
        return redirect('manage_staff')

def delete_staff(request, staff_id):
    try:
        staff = get_object_or_404(Staff, id=staff_id)
        staff.is_active =  False
        staff.user.is_active =  False
        staff.save(update_fields=['is_active'])
        staff.user.save(update_fields=['is_active'])
        if not staff.is_active:
            messages.success(request, "Staff member deactivated successfully!")
        else:
            messages.error(request, "Failed to deactivate staff member. Please try again.")
    except ObjectDoesNotExist:
        messages.error(request, "Staff member not found.")

    return redirect(reverse('manage_staff'))

@csrf_exempt
def get_dep_by_div(request):
    division_id = request.GET.get('division_id')
    departments = Department.objects.filter(division_id=division_id).values('id', 'name')
    return JsonResponse(list(departments), safe=False)

@csrf_exempt
def get_des_by_dep(request):
    department_id = request.GET.get('department_id')
    designations = Designation.objects.filter(department_id=department_id).values('id', 'name')
    return JsonResponse(list(designations), safe=False)

@csrf_exempt
def check_email_availability(request):
    email = request.POST.get("email")
    try:
        user = CustomUser.objects.filter(email=email).exists()
        if user:
            return HttpResponse(True)
        return HttpResponse(False)
    except Exception as e:
        return HttpResponse(False)


def edit_payslip(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            selected_staff_data = data.get('staffs', [])

            staffs = []
            for staff_data in selected_staff_data:
                staff_id = staff_data.get('staffId')
                month_str = staff_data.get('month')

                try:
                    staff_id = int(staff_id.split("-")[0])
                    staff = get_object_or_404(Staff, id=staff_id)
                    month_date = datetime.strptime(month_str, '%Y-%m-%d').date()
                except (ValueError, IndexError, Staff.DoesNotExist):
                    return JsonResponse({'success': False, 'error': f'Invalid month or staff ID: {staff_id}, {month_str}'}, status=400)

                payslip = Payslip.objects.filter(staff=staff, month=month_date).first()
                if not payslip:
                    return JsonResponse({'success': False, 'error': f'Payslip not found for staff ID {staff_id} and month {month_str}.'}, status=404)

                if staff.employment_type in ['Active', 'Contract']:
                    try:
                        staff.contract_pay = ContractPay.objects.get(staff=staff)
                        staff.deductions = Deductions.objects.get(staff=staff)
                    except (Deductions.DoesNotExist, ContractPay.DoesNotExist):
                        staff.deductions = 0
                        staff.contract_pay = 0

                if staff.employment_type == 'Regular':
                    try:
                        staff.deductions = Deductions.objects.filter(staff=staff)
                        staff.regular_pay = RegularPay.objects.filter(staff=staff)
                    except (Deductions.DoesNotExist, RegularPay.DoesNotExist):
                        staff.deductions = 0
                        staff.regular_pay = 0

                staffs.append(staff)

            if not staffs:
                return JsonResponse({'success': False, 'error': 'No valid staff selected.'}, status=404)

            # Determine which template to render based on staff employment type
            employment_types = set(staff.employment_type for staff in staffs)
            if 'Active' in employment_types:
                rendered_template = render(request, "admin_template/edit_payslip_active.html", {'staffs': staffs, 'page_title': 'Edit Payslip For Active Staff'})
            elif 'Contract' in employment_types:
                rendered_template = render(request, "admin_template/edit_payslip_contract.html", {'staffs': staffs, 'page_title': 'Edit Payslip For Contract Staff'})
            elif 'Regular' in employment_types:
                latest_month = Fixed.objects.aggregate(latest_month=Max('month'))['latest_month']
                divisions_with_latest_values = Fixed.objects.filter(month=latest_month)
                rendered_template = render(request, "admin_template/edit_payslip_regular.html", {'latest_entry': divisions_with_latest_values,'staffs': staffs, 'page_title': 'Edit Payslip For Regular Staff'})
            else:
                return JsonResponse({'success': False, 'error': 'No valid employment type found.'}, status=400)

            return HttpResponse(rendered_template.content.decode('utf-8'), content_type='text/html')

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid data format.'}, status=400)
    else:
        return JsonResponse({'success': False, 'error': 'Invalid request method.'}, status=405)

def edit_payroll_regular(request):
    medical_allowance = request.GET.get('give_medical', 'no')
    staff_members = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Regular')

    for staff in staff_members:
            try:
                fixed = Fixed.objects.filter(division=staff.staff.division).latest('month')
                month = fixed.month
            except Fixed.DoesNotExist:
                messages.error(request, "No fixed allowance data found. Please add fixed allowance data first.")
                return redirect('generate_payslip_regular')
            try:
                deductions = Deductions.objects.filter(staff=staff.staff.id).first()
                attendance = Attendance.objects.filter(staff=staff.staff.id).first()
                regular_pay = RegularPay.objects.filter(staff=staff.staff.id).first()
                grade = Grade.objects.get(id=staff.staff.grade_id)
            except (Deductions.DoesNotExist, Attendance.DoesNotExist, RegularPay.DoesNotExist, Grade.DoesNotExist) as e:
                messages.error(request, f"Missing data for {staff.user.get_full_name()}: {e}")
                continue
            except Exception as e:
                messages.error(request, f"Error processing payroll for {staff.user.get_full_name()}: {e}")
                continue

            _, num_days_in_month = monthrange(month.year, month.month)

            basic_salary = staff.staff.basic_amt
            working_days = attendance.paid_days + attendance.lop
            basic = int((working_days * basic_salary) / num_days_in_month)

            total_deductions = (
                (deductions.society or 0) + (deductions.income_tax or 0) +
                (deductions.canteen or 0) + (deductions.advance or 0) +
                (deductions.insurance or 0) + (deductions.other or 0)
            )

            dp = math.ceil(basic * 0.5)
            da = float(fixed.da)
            da = math.ceil((basic + dp) * da)
            hra = float(fixed.hra)
            hra = math.ceil((basic + dp) * hra)
            gross_pay = basic + dp + da + hra + grade.conva + grade.adhoc + staff.staff.cca + regular_pay.arrears + regular_pay.other

            medical = 0
            if medical_allowance == "yes":
               medical = 3*(basic + dp + da)

            epf = math.ceil((basic + dp + da) * 0.12)  # Assuming EPF is 12% of basic salary
            esi = math.ceil(gross_pay * 0.0175 if gross_pay <= 21000 else 0)  # ESI calculation
            total_deductions = float(total_deductions) + epf + esi
            net_pay = gross_pay - total_deductions

            # Update or create the payslip
            payslip, created = Payslip.objects.update_or_create(
                month=fixed.month,
                staff=staff.staff,
                defaults={
                    'basic': basic,
                    'da': da,
                    'dp': dp,
                    'hra': hra,
                    'conv': grade.conva,
                    'cca': staff.staff.cca,
                    'adhoc': grade.adhoc,
                    'medical': medical,
                    'arrears': regular_pay.arrears,
                    'Dother': deductions.other,
                    'Pother': regular_pay.other,
                    'gross_pay': gross_pay,
                    'epf': epf,
                    'esi': esi,
                    'income_tax': deductions.income_tax,
                    'canteen': deductions.canteen,
                    'society': deductions.society,
                    'advance': deductions.advance,
                    'insurance': deductions.insurance,
                    'total_deductions': total_deductions,
                    'net_pay': net_pay,
                    'paid_days': attendance.paid_days,
                    'lop': attendance.lop,
                }
            )

    messages.info(request, f"Updated payslip Successfully!")
    return redirect(reverse('list_payslip_type', args=['Regular']))

def edit_payroll_contract(request):
    staff_members = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Contract')

    for staff in staff_members:
        try:
            contract_pay = ContractPay.objects.filter(staff=staff.staff.id).first()
            deductions = Deductions.objects.filter(staff=staff.staff.id).first()
            attendance = Attendance.objects.filter(staff=staff.staff.id).first()
        except (Deductions.DoesNotExist, Attendance.DoesNotExist, ContractPay.DoesNotExist) as e:
            messages.error(request, f"Missing data for {staff.user.get_full_name()}: {e}")
            continue
        except Exception as e:
            messages.error(request, f"Error processing payroll for {staff.user.get_full_name()}: {e}")
            continue

        month = contract_pay.month
        _, num_days_in_month = monthrange(month.year, month.month)
        paid_days = attendance.paid_days
        lop = attendance.lop
        working_days = attendance.paid_days + attendance.lop
        basic_salary = staff.staff.basic_amt
        basic = (working_days * basic_salary) / num_days_in_month

        total_deductions = (
            (deductions.society or 0) + (deductions.income_tax or 0) +
            (deductions.canteen or 0) + (deductions.advance or 0) +
            (deductions.insurance or 0) + (deductions.other or 0)
        )

        hra = contract_pay.hra
        adhoc = contract_pay.adhoc
        gross_pay = basic + hra + adhoc
        epf = math.ceil(basic * 0.12)
        esi = math.ceil(gross_pay * 0.0075 if gross_pay <= 21000 else 0)
        total_deductions = total_deductions + epf + esi
        net_pay = gross_pay - total_deductions

        # Update or create the payslip
        payslip, created = Payslip.objects.update_or_create(
            month=contract_pay.month,
            staff=staff.staff,
            defaults={
                'basic': basic,
                'hra': hra,
                'adhoc': adhoc,
                'arrears': contract_pay.arrears,
                'Dother': deductions.other,
                'Pother': contract_pay.other,
                'gross_pay': gross_pay,
                'epf': epf,
                'esi': esi,
                'income_tax': deductions.income_tax,
                'canteen': deductions.canteen,
                'society': deductions.society,
                'advance': deductions.advance,
                'insurance': deductions.insurance,
                'total_deductions': total_deductions,
                'net_pay': net_pay,
                'paid_days': attendance.paid_days,
                'lop': attendance.lop,
            }
        )

    messages.info(request, f"Updated payslip Successfully!")

    return redirect(reverse('list_payslip_type', args=['Contract']))

def edit_payroll_active(request):
    staff_members = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Active')

    for staff in staff_members:
        try:
            contract_pay = ContractPay.objects.filter(staff=staff.staff.id).first()
            deductions = Deductions.objects.filter(staff=staff.staff.id).first()
            attendance = Attendance.objects.filter(staff=staff.staff.id).first()
        except (Deductions.DoesNotExist, Attendance.DoesNotExist, ContractPay.DoesNotExist) as e:
            messages.error(request, f"Missing data for {staff.user.get_full_name()}: {e}")
            continue
        except Exception as e:
            messages.error(request, f"Error processing payroll for {staff.user.get_full_name()}: {e}")
            continue

        month = contract_pay.month
        _, num_days_in_month = monthrange(month.year, month.month)

        working_days = attendance.paid_days + attendance.lop
        basic_salary = staff.staff.basic_amt
        basic = int((working_days * basic_salary) / num_days_in_month)

        total_deductions = (
            (deductions.society or 0) + (deductions.income_tax or 0) +
            (deductions.canteen or 0) + (deductions.advance or 0) +
            (deductions.insurance or 0) + (deductions.other or 0)
        )

        hra = contract_pay.hra
        adhoc = contract_pay.adhoc
        gross_pay = basic + hra + adhoc
        net_pay = gross_pay - total_deductions

        # Check if a payslip for this staff member and month already exists
        payslip, created = Payslip.objects.update_or_create(
            month=contract_pay.month,
            staff=staff.staff,
            defaults={
                'basic': basic,
                'hra': hra,
                'adhoc': adhoc,
                'arrears': contract_pay.arrears,
                'Dother': deductions.other,
                'Pother': contract_pay.other,
                'gross_pay': gross_pay,
                'income_tax': deductions.income_tax,
                'canteen': deductions.canteen,
                'society': deductions.society,
                'advance': deductions.advance,
                'insurance': deductions.insurance,
                'total_deductions': total_deductions,
                'net_pay': net_pay,
                'paid_days': attendance.paid_days,
                'lop': attendance.lop,
            }
        )

    messages.info(request, f"Updated payslip Successfully! ")
    return redirect(reverse('list_payslip_type', args=['Active']))