from pathlib import Path
from dotenv import load_dotenv
import os
import sys

# Load environment variables from a .env file
load_dotenv()

# Base directory of the project
BASE_DIR = Path(__file__).resolve().parent.parent

# Secret key for cryptographic operations
SECRET_KEY = os.environ.get('SECRET_KEY')
if not SECRET_KEY:
    sys.exit("Error: SECRET_KEY not set in environment variables.")

# Debug mode (ensure it's properly set to a boolean)
DEBUG = os.environ.get('DEBUG', 'False').lower() in ('true', '1', 'yes', 'on')

# Allowed hosts (read from environment variable and split by comma)
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Installed applications
INSTALLED_APPS = [
    # Admin theme (must be before django.contrib.admin)
    'jazzmin',

    # Django apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Security apps
    # 'admin_honeypot',  # Temporarily disabled due to Django 4.2 compatibility

    # Custom apps
    'main',
]

# Middleware configuration
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    # Third-party middleware
    'whitenoise.middleware.WhiteNoiseMiddleware',
    # Custom middleware
    'main.backends.middleware.LoginCheckMiddleWare',
]

# Root URL configuration
ROOT_URLCONF = 'PMS.urls'

# Templates configuration
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# WSGI application
WSGI_APPLICATION = 'PMS.wsgi.application'

# Database configuration
if DEBUG :
    # Use SQLite for development if MySQL is not configured
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
else:
    # Use MySQL for production or when properly configured
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.environ.get('DB_NAME'),
            'USER': os.environ.get('DB_USER'),
            'PASSWORD': os.environ.get('DB_PASSWORD'),
            'HOST': os.environ.get('DB_HOST'),
            'PORT': os.environ.get('DB_PORT'),
            'OPTIONS': {
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'"
            }
        }
    }

# Ensure all required database environment variables are set (only in production)
if not DEBUG:
    required_db_vars = ['DB_NAME', 'DB_USER', 'DB_HOST', 'DB_PORT']
    missing_db_vars = [var for var in required_db_vars if not os.environ.get(var)]
    if missing_db_vars:
        sys.exit(f"Error: Missing database configuration variables: {', '.join(missing_db_vars)}")
else:
    # In development, provide helpful warnings for missing DB vars
    required_db_vars = ['DB_NAME', 'DB_USER', 'DB_HOST', 'DB_PORT']
    missing_db_vars = [var for var in required_db_vars if not os.environ.get(var)]
    if missing_db_vars:
        print(f"Warning: Missing database configuration variables: {', '.join(missing_db_vars)}")
        print("Please update your .env file with the correct database settings.")

# Password validation configuration
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Localization settings
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Asia/Kolkata'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images) configuration
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Custom user model
AUTH_USER_MODEL = 'main.CustomUser'
AUTHENTICATION_BACKENDS = [
    'main.backends.EmailBackend.EmailBackend',
]

# WhiteNoise static files storage
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Email server configuration
EMAIL_HOST = os.environ.get('EMAIL_HOST')
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_ADDRESS')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_PASSWORD')

# Ensure all required email environment variables are set (only in production)
if not DEBUG:
    required_email_vars = ['EMAIL_HOST', 'EMAIL_ADDRESS', 'EMAIL_PASSWORD']
    missing_email_vars = [var for var in required_email_vars if not os.environ.get(var)]
    if missing_email_vars:
        sys.exit(f"Error: Missing email configuration variables: {', '.join(missing_email_vars)}")
else:
    # In development, use console backend if email vars are missing
    required_email_vars = ['EMAIL_HOST', 'EMAIL_ADDRESS', 'EMAIL_PASSWORD']
    missing_email_vars = [var for var in required_email_vars if not os.environ.get(var)]
    if missing_email_vars:
        print(f"Warning: Missing email configuration variables: {', '.join(missing_email_vars)}")
        print("Using console email backend for development.")
        EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Google reCAPTCHA configuration
GOOGLE_RECAPTCHA_SECRET_KEY = os.environ.get('GOOGLE_RECAPTCHA_SECRET_KEY')
GOOGLE_RECAPTCHA_SITE_KEY = os.environ.get('GOOGLE_RECAPTCHA_SITE_KEY')

# Ensure all required reCAPTCHA environment variables are set (only in production)
if not DEBUG:
    required_recaptcha_vars = ['GOOGLE_RECAPTCHA_SECRET_KEY', 'GOOGLE_RECAPTCHA_SITE_KEY']
    missing_recaptcha_vars = [var for var in required_recaptcha_vars if not os.environ.get(var)]
    if missing_recaptcha_vars:
        sys.exit(f"Error: Missing reCAPTCHA configuration variables: {', '.join(missing_recaptcha_vars)}")

# Development settings
if DEBUG:
    # Add django-debug-toolbar if available
    try:
        import debug_toolbar
        INSTALLED_APPS.append('debug_toolbar')
        MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
        INTERNAL_IPS = ['127.0.0.1', 'localhost']
    except ImportError:
        pass

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Session settings
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# Security settings (adjust for production)
if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

# Jazzmin Admin Theme Configuration
JAZZMIN_SETTINGS = {
    # Title of the window (Will default to current_admin_site.site_title if absent or None)
    "site_title": "PMS Admin",

    # Title on the login screen (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_header": "PMS",

    # Title on the brand (19 chars max) (defaults to current_admin_site.site_header if absent or None)
    "site_brand": "Payroll Management",

    # Logo to use for your site, must be present in static files, used for brand on top left
    "site_logo": "img/nitra.png",

    # Logo to use for your site, must be present in static files, used for login form logo (defaults to site_logo)
    "login_logo": "img/nitra.png",

    # Logo to use for login form in dark themes (defaults to login_logo)
    "login_logo_dark": "img/nitra.png",

    # CSS classes that are applied to the logo above
    "site_logo_classes": "img-circle",

    # Relative path to a favicon for your site, will default to site_logo if absent (ideally 32x32 px)
    "site_icon": "img/nitra.png",

    # Welcome text on the login screen
    "welcome_sign": "Welcome to PMS Admin",

    # Copyright on the footer
    "copyright": "Payroll Management System",


    # Field name on user model that contains avatar ImageField/URLField/Charfield or a callable that receives the user
    "user_avatar": "profile_pic",

    ############
    # Top Menu #
    ############

    # Links to put along the top menu
    "topmenu_links": [
        # Url that gets reversed (Permissions can be added)
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},
    ],


    #############
    # Side Menu #
    #############

    # Whether to display the side menu
    "show_sidebar": True,

    # Whether to aut expand the menu
    "navigation_expanded": True,

    # Hide these apps when generating side menu e.g (auth)
    "hide_apps": [],

    # Hide these models when generating side menu (e.g auth.user)
    "hide_models": [],

    # List of apps (and/or models) to base side menu ordering off of (does not need to contain all apps/models)
    "order_with_respect_to": ["auth", "main"],

    # Custom links to append to app groups, keyed on app name
    "custom_links": {
        "main": [{
            "name": "Generate Payslips",
            "url": "admin:main_payslip_changelist",
            "icon": "fas fa-money-bill",
            "permissions": ["main.view_payslip"]
        }]
    },

    # Custom icons for side menu apps/models See https://fontawesome.com/icons?d=gallery&m=free&v=5.0.0,5.0.1,5.0.10,5.0.11,5.0.12,5.0.13,5.0.2,5.0.3,5.0.4,5.0.5,5.0.6,5.0.7,5.0.8,5.0.9,5.1.0,5.1.1,5.2.0,5.3.0,5.3.1,5.4.0,5.4.1,5.4.2,5.5.0,5.6.0,5.6.1,5.6.3,5.7.0,5.7.1,5.7.2,5.8.0,5.8.1,5.8.2,5.9.0,5.10.0,5.10.1,5.10.2,5.11.0,5.11.1,5.11.2,5.12.0,5.12.1,5.13.0,5.13.1,5.14.0,5.15.0,5.15.1,5.15.2,5.15.3,5.15.4&s=solid
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "main.CustomUser": "fas fa-user-circle",
        "main.Admin": "fas fa-user-shield",
        "main.Accountant": "fas fa-calculator",
        "main.Staff": "fas fa-user-tie",
        "main.Division": "fas fa-sitemap",
        "main.Department": "fas fa-building",
        "main.Designation": "fas fa-id-badge",
        "main.Grade": "fas fa-layer-group",
        "main.Payslip": "fas fa-money-bill-wave",
        "main.Attendance": "fas fa-calendar-check",
        "main.Deductions": "fas fa-minus-circle",
        "main.Fixed": "fas fa-cog",
        "main.ContractPay": "fas fa-file-contract",
        "main.RegularPay": "fas fa-money-check",
    },

    # Icons that are used when one is not manually specified
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",

    #################
    # Related Modal #
    #################
    # Use modals instead of popups
    "related_modal_active": False,

    #############
    # UI Tweaks #
    #############
    # Relative paths to custom CSS/JS scripts (must be present in static files)
    "custom_css": None,
    "custom_js": None,
    # Whether to link font from fonts.googleapis.com (use custom_css to supply font otherwise)
    "use_google_fonts_cdn": True,
    # Whether to show the UI customizer on the sidebar
    "show_ui_builder": False,

    ###############
    # Change view #
    ###############
    # Render out the change view as a single form, or in tabs, current options are
    # - single
    # - horizontal_tabs (default)
    # - vertical_tabs
    # - collapsible
    # - carousel
    "changeform_format": "horizontal_tabs",
    # override change forms on a per modeladmin basis
    "changeform_format_overrides": {"auth.user": "collapsible", "auth.group": "vertical_tabs"},
    # Add a language dropdown into the admin
    "language_chooser": False,
}

JAZZMIN_UI_TWEAKS = {
    "navbar_small_text": False,
    "footer_small_text": False,
    "body_small_text": False,
    "brand_small_text": False,
    "accent": "accent-primary",
    "navbar": "navbar-primary navbar-dark",
    "no_navbar_border": False,
    "navbar_fixed": False,
    "layout_boxed": False,
    "footer_fixed": False,
    "sidebar_fixed": False,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": False,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    "theme": "default",
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success"
    }
}
